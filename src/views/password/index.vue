<template>
  <div class="password-manager">
    <!-- 粒子背景 -->
    <div ref="particleContainer" class="particle-container"></div>

    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="shape shape1"></div>
      <div class="shape shape2"></div>
      <div class="shape shape3"></div>
      <div class="shape shape4"></div>
      <div class="shape shape5"></div>
      <div class="shape shape6"></div>
    </div>

    <!-- 顶部导航 -->
    <div class="top-nav">
      <div class="glass-card nav-card">
        <div class="nav-content">
          <div class="logo-section">
            <div class="logo-icon">
              <i class="el-icon-lock"></i>
            </div>
            <span class="logo-text">SecureVault</span>
          </div>
          <div class="nav-actions">
            <el-button type="text" class="nav-btn" @click="showSettings">
              <i class="el-icon-setting"></i>
            </el-button>
            <el-button type="text" class="nav-btn" @click="exportData">
              <i class="el-icon-download"></i>
            </el-button>
            <el-button type="text" class="nav-btn" @click="showProfile">
              <i class="el-icon-user"></i>
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 主容器 -->
    <div class="main-container">
      <!-- 统计仪表板 -->
      <div class="dashboard-section">
        <div class="stats-grid">
          <div class="glass-card stat-card total-passwords">
            <div class="stat-content">
              <div class="stat-icon">
                <i class="el-icon-files"></i>
              </div>
              <div class="stat-info">
                <h3>{{ passwords.length }}</h3>
                <p>密码总数</p>
              </div>
            </div>
            <div class="stat-chart">
              <div class="chart-ring" :style="{ '--progress': totalProgress }"></div>
            </div>
          </div>

          <div class="glass-card stat-card strong-passwords">
            <div class="stat-content">
              <div class="stat-icon">
                <i class="el-icon-medal-1"></i>
              </div>
              <div class="stat-info">
                <h3>{{ strongPasswordCount }}</h3>
                <p>强密码</p>
              </div>
            </div>
            <div class="strength-indicator">
              <div class="strength-bar strong" :style="{ width: strengthPercentage + '%' }"></div>
            </div>
          </div>

          <div class="glass-card stat-card recent-activity">
            <div class="stat-content">
              <div class="stat-icon">
                <i class="el-icon-time"></i>
              </div>
              <div class="stat-info">
                <h3>{{ recentlyAddedCount }}</h3>
                <p>最近添加</p>
              </div>
            </div>
            <div class="activity-dots">
              <div class="dot active"></div>
              <div class="dot"></div>
              <div class="dot"></div>
            </div>
          </div>

          <div class="glass-card stat-card security-score">
            <div class="stat-content">
              <div class="stat-icon">
                <i class="el-icon-star-on"></i>
              </div>
              <div class="stat-info">
                <h3>{{ securityScore }}%</h3>
                <p>安全评分</p>
              </div>
            </div>
            <div class="score-gauge">
              <div
                class="gauge-fill"
                :style="{ transform: `rotate(${securityScore * 1.8}deg)` }"
              ></div>
            </div>
          </div>
        </div>
      </div>

      <!-- 快速操作栏 -->
      <div class="quick-actions">
        <div class="glass-card actions-card">
          <div class="actions-content">
            <div class="action-item" @click="showAddDialog">
              <div class="action-icon add">
                <i class="el-icon-plus"></i>
              </div>
              <span>添加密码</span>
            </div>
            <div class="action-item" @click="generateRandomPassword">
              <div class="action-icon generate">
                <i class="el-icon-refresh"></i>
              </div>
              <span>生成密码</span>
            </div>
            <div class="action-item" @click="checkSecurity">
              <div class="action-icon scan">
                <i class="el-icon-view"></i>
              </div>
              <span>安全检测</span>
            </div>
            <div class="action-item" @click="showImportDialog">
              <div class="action-icon import">
                <i class="el-icon-upload2"></i>
              </div>
              <span>导入数据</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 搜索和筛选 -->
      <div class="filter-section">
        <div class="glass-card filter-card">
          <div class="filter-content">
            <div class="search-box">
              <el-input
                v-model="searchKeyword"
                placeholder="搜索网站、用户名或备注..."
                prefix-icon="el-icon-search"
                class="search-input"
                clearable
                @input="handleSearch"
              >
                <el-button slot="append" icon="el-icon-search" @click="advancedSearch"></el-button>
              </el-input>
            </div>
            <div class="filter-controls">
              <el-select
                v-model="categoryFilter"
                placeholder="分类"
                class="filter-select"
                clearable
              >
                <el-option label="全部分类" value=""></el-option>
                <el-option label="社交媒体" value="social">
                  <i class="el-icon-chat-line-round"></i> 社交媒体
                </el-option>
                <el-option label="邮箱" value="email">
                  <i class="el-icon-message"></i> 邮箱
                </el-option>
                <el-option label="工作" value="work">
                  <i class="el-icon-suitcase"></i> 工作
                </el-option>
                <el-option label="购物" value="shopping">
                  <i class="el-icon-shopping-cart-full"></i> 购物
                </el-option>
                <el-option label="银行" value="bank"> <i class="el-icon-coin"></i> 银行 </el-option>
                <el-option label="娱乐" value="entertainment">
                  <i class="el-icon-video-play"></i> 娱乐
                </el-option>
                <el-option label="其他" value="other">
                  <i class="el-icon-more"></i> 其他
                </el-option>
              </el-select>

              <el-select
                v-model="strengthFilter"
                placeholder="强度"
                class="filter-select"
                clearable
              >
                <el-option label="全部强度" value=""></el-option>
                <el-option label="强密码" value="strong"></el-option>
                <el-option label="中等密码" value="medium"></el-option>
                <el-option label="弱密码" value="weak"></el-option>
              </el-select>

              <el-select v-model="sortBy" placeholder="排序" class="filter-select">
                <el-option label="按时间排序" value="date"></el-option>
                <el-option label="按名称排序" value="name"></el-option>
                <el-option label="按强度排序" value="strength"></el-option>
              </el-select>

              <el-button-group class="view-toggle">
                <el-button
                  :type="viewMode === 'grid' ? 'primary' : ''"
                  icon="el-icon-menu"
                  @click="viewMode = 'grid'"
                ></el-button>
                <el-button
                  :type="viewMode === 'list' ? 'primary' : ''"
                  icon="el-icon-tickets"
                  @click="viewMode = 'list'"
                ></el-button>
              </el-button-group>
            </div>
          </div>
        </div>
      </div>

      <!-- 密码列表 -->
      <div class="passwords-section">
        <div class="glass-card passwords-card">
          <div class="passwords-header">
            <h2>我的密码</h2>
            <div class="header-actions">
              <el-button size="small" @click="selectAll">
                {{ allSelected ? '取消全选' : '全选' }}
              </el-button>
              <el-button
                v-if="selectedPasswords.length > 0"
                size="small"
                type="danger"
                @click="deleteSelected"
              >
                删除选中 ({{ selectedPasswords.length }})
              </el-button>
            </div>
          </div>

          <!-- 网格视图 -->
          <div v-if="viewMode === 'grid'" class="passwords-grid">
            <div
              v-for="item in sortedAndFilteredPasswords"
              :key="item.id"
              class="password-item glass-item"
              :class="{ selected: selectedPasswords.includes(item.id) }"
            >
              <div class="item-header">
                <el-checkbox
                  v-model="selectedPasswords"
                  :label="item.id"
                  @change="handleSelection"
                ></el-checkbox>
                <div class="favorite-btn" @click="toggleFavorite(item)">
                  <i :class="item.favorite ? 'el-icon-star-on' : 'el-icon-star-off'"></i>
                </div>
              </div>

              <div class="password-main" @click="viewPasswordDetail(item)">
                <div class="password-icon">
                  <img
                    v-if="item.favicon"
                    :src="item.favicon"
                    :alt="item.website"
                    @error="handleImageError"
                  />
                  <div v-else class="default-icon" :class="getCategoryClass(item.category)">
                    <i :class="getCategoryIcon(item.category)"></i>
                  </div>
                </div>

                <div class="password-info">
                  <h3 class="website-name">{{ item.website }}</h3>
                  <p class="username">{{ item.username }}</p>
                  <div class="password-meta">
                    <div class="strength-indicator">
                      <div class="strength-dots">
                        <span
                          v-for="n in 3"
                          :key="n"
                          class="dot"
                          :class="getStrengthDotClass(item.password, n)"
                        ></span>
                      </div>
                      <span class="strength-text">{{ getStrengthText(item.password) }}</span>
                    </div>
                    <div class="last-updated">
                      {{ formatDate(item.updatedAt || item.createdAt) }}
                    </div>
                  </div>
                </div>
              </div>

              <div class="password-actions">
                <el-tooltip content="复制用户名" placement="top">
                  <el-button
                    type="text"
                    icon="el-icon-user"
                    @click.stop="copyToClipboard(item.username, '用户名')"
                  ></el-button>
                </el-tooltip>
                <el-tooltip content="复制密码" placement="top">
                  <el-button
                    type="text"
                    icon="el-icon-key"
                    @click.stop="copyToClipboard(item.password, '密码')"
                  ></el-button>
                </el-tooltip>
                <el-tooltip content="打开网站" placement="top">
                  <el-button
                    type="text"
                    icon="el-icon-link"
                    @click.stop="openWebsite(item.url)"
                  ></el-button>
                </el-tooltip>
                <el-dropdown trigger="click" @command="handleItemAction">
                  <el-button type="text" icon="el-icon-more"></el-button>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item :command="{ action: 'edit', item }">
                      <i class="el-icon-edit"></i> 编辑
                    </el-dropdown-item>
                    <el-dropdown-item :command="{ action: 'duplicate', item }">
                      <i class="el-icon-copy-document"></i> 复制
                    </el-dropdown-item>
                    <el-dropdown-item :command="{ action: 'share', item }">
                      <i class="el-icon-share"></i> 分享
                    </el-dropdown-item>
                    <el-dropdown-item :command="{ action: 'delete', item }" divided>
                      <i class="el-icon-delete"></i> 删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </el-dropdown>
              </div>
            </div>
          </div>

          <!-- 列表视图 -->
          <div v-else class="passwords-list">
            <el-table
              :data="sortedAndFilteredPasswords"
              style="width: 100%; background: transparent"
              :row-class-name="tableRowClassName"
              @selection-change="handleTableSelection"
            >
              <el-table-column type="selection" width="55"></el-table-column>
              <el-table-column label="网站" min-width="200">
                <template slot-scope="scope">
                  <div class="table-website">
                    <div class="website-icon">
                      <img
                        v-if="scope.row.favicon"
                        :src="scope.row.favicon"
                        :alt="scope.row.website"
                        @error="handleImageError"
                      />
                      <i v-else :class="getCategoryIcon(scope.row.category)"></i>
                    </div>
                    <div class="website-info">
                      <div class="name">{{ scope.row.website }}</div>
                      <div class="url">{{ scope.row.url }}</div>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="username" label="用户名" min-width="150"></el-table-column>
              <el-table-column label="密码强度" width="120">
                <template slot-scope="scope">
                  <div class="table-strength">
                    <div class="strength-bar" :class="getStrengthClass(scope.row.password)"></div>
                    <span class="strength-text">{{ getStrengthText(scope.row.password) }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="category" label="分类" width="100">
                <template slot-scope="scope">
                  <el-tag :type="getCategoryTagType(scope.row.category)" size="mini">
                    {{ getCategoryName(scope.row.category) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="更新时间" width="120">
                <template slot-scope="scope">
                  {{ formatDate(scope.row.updatedAt || scope.row.createdAt) }}
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200" fixed="right">
                <template slot-scope="scope">
                  <el-button
                    type="text"
                    size="small"
                    @click="copyToClipboard(scope.row.password, '密码')"
                  >
                    复制
                  </el-button>
                  <el-button type="text" size="small" @click="editPassword(scope.row)">
                    编辑
                  </el-button>
                  <el-button type="text" size="small" @click="deletePassword(scope.row)">
                    删除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 空状态 -->
          <div v-if="sortedAndFilteredPasswords.length === 0" class="empty-state">
            <div class="empty-icon">
              <i class="el-icon-box"></i>
            </div>
            <h3>暂无密码数据</h3>
            <p>开始添加你的第一个密码吧</p>
            <el-button type="primary" @click="showAddDialog">添加密码</el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加/编辑密码弹窗 -->
    <el-dialog
      :title="isEdit ? '编辑密码' : '添加密码'"
      :visible.sync="dialogVisible"
      width="600px"
      class="password-dialog"
      :close-on-click-modal="false"
    >
      <div class="dialog-form">
        <el-tabs v-model="activeTab" class="form-tabs">
          <el-tab-pane label="基本信息" name="basic">
            <el-form ref="passwordForm" :model="currentPassword" :rules="rules" label-width="100px">
              <el-form-item label="网站名称" prop="website">
                <el-input
                  v-model="currentPassword.website"
                  placeholder="请输入网站名称"
                  @blur="autoFillWebsiteInfo"
                ></el-input>
              </el-form-item>

              <el-form-item label="网站地址" prop="url">
                <el-input
                  v-model="currentPassword.url"
                  placeholder="https://example.com"
                  @blur="updateFavicon"
                >
                  <template slot="prepend">
                    <i class="el-icon-link"></i>
                  </template>
                </el-input>
              </el-form-item>

              <el-form-item label="用户名" prop="username">
                <el-input v-model="currentPassword.username" placeholder="请输入用户名或邮箱">
                  <template slot="prepend">
                    <i class="el-icon-user"></i>
                  </template>
                </el-input>
              </el-form-item>

              <el-form-item label="密码" prop="password">
                <div class="password-input-group">
                  <el-input
                    v-model="currentPassword.password"
                    :type="showPassword ? 'text' : 'password'"
                    placeholder="请输入密码"
                    class="password-field"
                    @input="updatePasswordStrength"
                  >
                    <template slot="prepend">
                      <i class="el-icon-key"></i>
                    </template>
                    <el-button
                      slot="suffix"
                      type="text"
                      :icon="showPassword ? 'el-icon-view' : 'el-icon-hide'"
                      @click="showPassword = !showPassword"
                    ></el-button>
                  </el-input>
                  <div class="password-tools">
                    <el-button type="text" icon="el-icon-refresh" @click="generateStrongPassword">
                      生成强密码
                    </el-button>
                    <div class="strength-meter">
                      <div class="strength-label">强度:</div>
                      <div class="strength-bars">
                        <div
                          v-for="n in 4"
                          :key="n"
                          class="strength-bar"
                          :class="getPasswordStrengthBarClass(n)"
                        ></div>
                      </div>
                      <span class="strength-text">{{ currentPasswordStrength }}</span>
                    </div>
                  </div>
                </div>
              </el-form-item>

              <el-form-item label="分类" prop="category">
                <el-select v-model="currentPassword.category" placeholder="请选择分类">
                  <el-option label="社交媒体" value="social">
                    <i class="el-icon-chat-line-round"></i> 社交媒体
                  </el-option>
                  <el-option label="邮箱" value="email">
                    <i class="el-icon-message"></i> 邮箱
                  </el-option>
                  <el-option label="工作" value="work">
                    <i class="el-icon-suitcase"></i> 工作
                  </el-option>
                  <el-option label="购物" value="shopping">
                    <i class="el-icon-shopping-cart-full"></i> 购物
                  </el-option>
                  <el-option label="银行" value="bank">
                    <i class="el-icon-coin"></i> 银行
                  </el-option>
                  <el-option label="娱乐" value="entertainment">
                    <i class="el-icon-video-play"></i> 娱乐
                  </el-option>
                  <el-option label="其他" value="other">
                    <i class="el-icon-more"></i> 其他
                  </el-option>
                </el-select>
              </el-form-item>
            </el-form>
          </el-tab-pane>

          <el-tab-pane label="高级设置" name="advanced">
            <el-form :model="currentPassword" label-width="120px">
              <el-form-item label="备注信息">
                <el-input
                  v-model="currentPassword.notes"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入备注信息"
                ></el-input>
              </el-form-item>

              <el-form-item label="标签">
                <el-tag
                  v-for="tag in currentPassword.tags"
                  :key="tag"
                  :closable="true"
                  class="tag-item"
                  @close="removeTag(tag)"
                >
                  {{ tag }}
                </el-tag>
                <el-input
                  v-if="inputVisible"
                  ref="saveTagInput"
                  v-model="inputValue"
                  size="small"
                  class="tag-input"
                  @keyup.enter.native="handleInputConfirm"
                  @blur="handleInputConfirm"
                ></el-input>
                <el-button v-else class="button-new-tag" size="small" @click="showInput">
                  + 添加标签
                </el-button>
              </el-form-item>

              <el-form-item label="收藏">
                <el-switch v-model="currentPassword.favorite"></el-switch>
              </el-form-item>

              <el-form-item label="密码提醒">
                <el-switch
                  v-model="currentPassword.reminder"
                  active-text="开启密码过期提醒"
                ></el-switch>
              </el-form-item>

              <el-form-item v-if="currentPassword.reminder" label="提醒周期">
                <el-select v-model="currentPassword.reminderDays" placeholder="选择提醒周期">
                  <el-option label="30天" :value="30"></el-option>
                  <el-option label="60天" :value="60"></el-option>
                  <el-option label="90天" :value="90"></el-option>
                  <el-option label="半年" :value="180"></el-option>
                  <el-option label="一年" :value="365"></el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="二步验证">
                <el-input
                  v-model="currentPassword.twoFactor"
                  placeholder="如果有二步验证,请输入密钥"
                ></el-input>
              </el-form-item>
            </el-form>
          </el-tab-pane>
        </el-tabs>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button v-if="!isEdit" @click="savePasswordAsDraft">保存草稿</el-button>
        <el-button type="primary" :loading="saving" @click="savePassword">
          {{ isEdit ? '保存修改' : '添加密码' }}
        </el-button>
      </span>
    </el-dialog>

    <!-- 密码详情弹窗 -->
    <el-dialog
      title="密码详情"
      :visible.sync="detailDialogVisible"
      width="500px"
      class="detail-dialog"
    >
      <div v-if="selectedPasswordDetail" class="password-detail">
        <div class="detail-header">
          <div class="detail-icon">
            <img
              v-if="selectedPasswordDetail.favicon"
              :src="selectedPasswordDetail.favicon"
              :alt="selectedPasswordDetail.website"
            />
            <i v-else :class="getCategoryIcon(selectedPasswordDetail.category)"></i>
          </div>
          <div class="detail-info">
            <h2>{{ selectedPasswordDetail.website }}</h2>
            <p>{{ selectedPasswordDetail.url }}</p>
          </div>
        </div>

        <div class="detail-content">
          <div class="detail-item">
            <label>用户名:</label>
            <div class="detail-value">
              <span>{{ selectedPasswordDetail.username }}</span>
              <el-button
                type="text"
                icon="el-icon-copy-document"
                @click="copyToClipboard(selectedPasswordDetail.username, '用户名')"
              ></el-button>
            </div>
          </div>

          <div class="detail-item">
            <label>密码:</label>
            <div class="detail-value">
              <span v-if="showDetailPassword">{{ selectedPasswordDetail.password }}</span>
              <span v-else>{{ '●'.repeat(selectedPasswordDetail.password.length) }}</span>
              <el-button
                type="text"
                :icon="showDetailPassword ? 'el-icon-view' : 'el-icon-hide'"
                @click="showDetailPassword = !showDetailPassword"
              ></el-button>
              <el-button
                type="text"
                icon="el-icon-copy-document"
                @click="copyToClipboard(selectedPasswordDetail.password, '密码')"
              ></el-button>
            </div>
          </div>

          <div class="detail-item">
            <label>密码强度:</label>
            <div class="detail-value">
              <div class="strength-indicator">
                <div
                  class="strength-bar"
                  :class="getStrengthClass(selectedPasswordDetail.password)"
                ></div>
                <span>{{ getStrengthText(selectedPasswordDetail.password) }}</span>
              </div>
            </div>
          </div>

          <div v-if="selectedPasswordDetail.notes" class="detail-item">
            <label>备注:</label>
            <div class="detail-value">
              <span>{{ selectedPasswordDetail.notes }}</span>
            </div>
          </div>

          <div class="detail-item">
            <label>创建时间:</label>
            <div class="detail-value">
              <span>{{ formatDateTime(selectedPasswordDetail.createdAt) }}</span>
            </div>
          </div>

          <div v-if="selectedPasswordDetail.updatedAt" class="detail-item">
            <label>更新时间:</label>
            <div class="detail-value">
              <span>{{ formatDateTime(selectedPasswordDetail.updatedAt) }}</span>
            </div>
          </div>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="editFromDetail">编辑</el-button>
      </span>
    </el-dialog>

    <!-- 导入数据弹窗 -->
    <el-dialog
      title="导入密码数据"
      :visible.sync="importDialogVisible"
      width="500px"
      class="import-dialog"
    >
      <div class="import-content">
        <el-upload
          class="upload-demo"
          drag
          action="#"
          :auto-upload="false"
          :on-change="handleFileChange"
          accept=".csv,.json"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div slot="tip" class="el-upload__tip">支持 CSV 和 JSON 格式文件</div>
        </el-upload>

        <div class="import-options">
          <h4>导入选项</h4>
          <el-checkbox v-model="importOptions.skipDuplicate">跳过重复项</el-checkbox>
          <el-checkbox v-model="importOptions.updateExisting">更新现有项</el-checkbox>
          <el-checkbox v-model="importOptions.createBackup">创建备份</el-checkbox>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="importDialogVisible = false">取消</el-button>
        <el-button type="primary" :loading="importing" @click="importData"> 开始导入 </el-button>
      </span>
    </el-dialog>

    <!-- 密码生成器弹窗 -->
    <el-dialog
      title="密码生成器"
      :visible.sync="generatorDialogVisible"
      width="500px"
      class="generator-dialog"
    >
      <div class="generator-content">
        <div class="generated-password">
          <el-input v-model="generatedPassword" readonly class="password-output">
            <el-button
              slot="append"
              icon="el-icon-copy-document"
              @click="copyToClipboard(generatedPassword, '生成的密码')"
            ></el-button>
          </el-input>
        </div>

        <div class="generator-options">
          <div class="option-group">
            <label>密码长度: {{ passwordLength }}</label>
            <el-slider
              v-model="passwordLength"
              :min="6"
              :max="50"
              @change="generatePassword"
            ></el-slider>
          </div>

          <div class="option-group">
            <el-checkbox v-model="includeUppercase" @change="generatePassword">
              包含大写字母 (A-Z)
            </el-checkbox>
          </div>

          <div class="option-group">
            <el-checkbox v-model="includeLowercase" @change="generatePassword">
              包含小写字母 (a-z)
            </el-checkbox>
          </div>

          <div class="option-group">
            <el-checkbox v-model="includeNumbers" @change="generatePassword">
              包含数字 (0-9)
            </el-checkbox>
          </div>

          <div class="option-group">
            <el-checkbox v-model="includeSymbols" @change="generatePassword">
              包含符号 (!@#$%^&*)
            </el-checkbox>
          </div>

          <div class="option-group">
            <el-checkbox v-model="excludeSimilar" @change="generatePassword">
              排除相似字符 (0, O, l, 1, I)
            </el-checkbox>
          </div>
        </div>

        <div class="generator-actions">
          <el-button icon="el-icon-refresh" @click="generatePassword"> 重新生成 </el-button>
          <el-button type="primary" @click="useGeneratedPassword"> 使用此密码 </el-button>
        </div>
      </div>
    </el-dialog>

    <!-- 安全检测结果弹窗 -->
    <el-dialog
      title="安全检测报告"
      :visible.sync="securityReportVisible"
      width="700px"
      class="security-dialog"
    >
      <div class="security-report">
        <div class="report-summary">
          <div class="summary-card">
            <div class="summary-icon">
              <i class="el-icon-shield" :style="{ color: getSecurityColor() }"></i>
            </div>
            <div class="summary-info">
              <h3>安全评分: {{ securityScore }}</h3>
              <p>{{ getSecurityLevel() }}</p>
            </div>
          </div>
        </div>

        <div class="report-details">
          <div class="detail-section">
            <h4>
              <i class="el-icon-warning" style="color: #f56c6c"></i>
              需要关注的问题
            </h4>
            <div class="issue-list">
              <div v-for="issue in securityIssues" :key="issue.type" class="issue-item">
                <div class="issue-header">
                  <span class="issue-title">{{ issue.title }}</span>
                  <el-tag :type="issue.severity === 'high' ? 'danger' : 'warning'" size="mini">
                    {{ issue.severity === 'high' ? '高危' : '中危' }}
                  </el-tag>
                </div>
                <p class="issue-description">{{ issue.description }}</p>
                <div class="affected-passwords">
                  <span>影响 {{ issue.count }} 个密码</span>
                  <el-button type="text" size="small" @click="showAffectedPasswords(issue)">
                    查看详情
                  </el-button>
                </div>
              </div>
            </div>
          </div>

          <div class="detail-section">
            <h4>
              <i class="el-icon-success" style="color: #67c23a"></i>
              安全状况良好
            </h4>
            <div class="good-practices">
              <div class="practice-item">
                <i class="el-icon-check"></i>
                <span>{{ strongPasswordCount }} 个强密码</span>
              </div>
              <div class="practice-item">
                <i class="el-icon-check"></i>
                <span>{{ uniquePasswordCount }} 个唯一密码</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'PasswordManager',
  data() {
    return {
      // 密码数据
      passwords: [
        {
          id: 1,
          website: 'GitHub',
          url: 'https://github.com',
          username: '<EMAIL>',
          password: 'SecurePass123!',
          category: 'work',
          notes: 'Development account',
          tags: ['development', 'code'],
          favorite: true,
          reminder: true,
          reminderDays: 90,
          twoFactor: '',
          createdAt: new Date('2024-01-15'),
          updatedAt: new Date('2024-01-20'),
          favicon: 'https://github.com/favicon.ico',
        },
        {
          id: 2,
          website: 'Gmail',
          url: 'https://gmail.com',
          username: '<EMAIL>',
          password: 'Gmail$ecure456',
          category: 'email',
          notes: 'Personal email account',
          tags: ['email', 'personal'],
          favorite: false,
          reminder: false,
          reminderDays: 60,
          twoFactor: '',
          createdAt: new Date('2024-01-10'),
          updatedAt: null,
          favicon: 'https://ssl.gstatic.com/ui/v1/icons/mail/rfr/gmail.ico',
        },
        {
          id: 3,
          website: 'Facebook',
          url: 'https://facebook.com',
          username: 'user123',
          password: 'weak123',
          category: 'social',
          notes: 'Social media account',
          tags: ['social'],
          favorite: false,
          reminder: true,
          reminderDays: 30,
          twoFactor: '',
          createdAt: new Date('2024-01-05'),
          updatedAt: null,
          favicon: 'https://static.xx.fbcdn.net/rsrc.php/yo/r/iRmz9lCMBD2.ico',
        },
      ],

      // 界面控制
      searchKeyword: '',
      categoryFilter: '',
      strengthFilter: '',
      sortBy: 'date',
      viewMode: 'grid',
      selectedPasswords: [],
      allSelected: false,

      // 弹窗控制
      dialogVisible: false,
      detailDialogVisible: false,
      importDialogVisible: false,
      generatorDialogVisible: false,
      securityReportVisible: false,

      // 表单数据
      currentPassword: this.getEmptyPassword(),
      isEdit: false,
      saving: false,
      importing: false,
      showPassword: false,
      activeTab: 'basic',

      // 标签输入
      inputVisible: false,
      inputValue: '',

      // 密码详情
      selectedPasswordDetail: null,
      showDetailPassword: false,

      // 导入选项
      importOptions: {
        skipDuplicate: true,
        updateExisting: false,
        createBackup: true,
      },
      importFile: null,

      // 密码生成器
      passwordLength: 16,
      includeUppercase: true,
      includeLowercase: true,
      includeNumbers: true,
      includeSymbols: true,
      excludeSimilar: false,
      generatedPassword: '',

      // 安全检测
      securityIssues: [],

      // 表单验证规则
      rules: {
        website: [{ required: true, message: '请输入网站名称', trigger: 'blur' }],
        url: [
          { required: true, message: '请输入网站地址', trigger: 'blur' },
          { type: 'url', message: '请输入有效的网址', trigger: 'blur' },
        ],
        username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, message: '密码长度至少6位', trigger: 'blur' },
        ],
        category: [{ required: true, message: '请选择分类', trigger: 'change' }],
      },
    };
  },

  computed: {
    // 统计数据
    strongPasswordCount() {
      return this.passwords.filter((p) => this.getPasswordStrength(p.password) >= 3).length;
    },

    recentlyAddedCount() {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      return this.passwords.filter((p) => new Date(p.createdAt) > thirtyDaysAgo).length;
    },

    securityScore() {
      if (this.passwords.length === 0) return 100;

      const strongCount = this.strongPasswordCount;
      const duplicateCount = this.getDuplicatePasswordCount();
      const weakCount = this.passwords.filter(
        (p) => this.getPasswordStrength(p.password) === 1
      ).length;

      let score = 100;
      score -= (weakCount / this.passwords.length) * 40;
      score -= (duplicateCount / this.passwords.length) * 30;
      score += (strongCount / this.passwords.length) * 10;

      return Math.max(0, Math.min(100, Math.round(score)));
    },

    strengthPercentage() {
      return this.passwords.length > 0
        ? (this.strongPasswordCount / this.passwords.length) * 100
        : 0;
    },

    totalProgress() {
      return this.passwords.length > 0 ? Math.min(100, this.passwords.length * 10) : 0;
    },

    uniquePasswordCount() {
      const uniquePasswords = new Set(this.passwords.map((p) => p.password));
      return uniquePasswords.size;
    },

    currentPasswordStrength() {
      return this.getStrengthText(this.currentPassword.password);
    },

    // 过滤和排序后的密码列表
    sortedAndFilteredPasswords() {
      let filtered = this.passwords.filter((password) => {
        const matchesSearch =
          !this.searchKeyword ||
          password.website.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
          password.username.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
          (password.notes &&
            password.notes.toLowerCase().includes(this.searchKeyword.toLowerCase()));

        const matchesCategory = !this.categoryFilter || password.category === this.categoryFilter;

        const matchesStrength =
          !this.strengthFilter || this.getStrengthLevel(password.password) === this.strengthFilter;

        return matchesSearch && matchesCategory && matchesStrength;
      });

      // 排序
      filtered.sort((a, b) => {
        switch (this.sortBy) {
          case 'name':
            return a.website.localeCompare(b.website);
          case 'strength':
            return this.getPasswordStrength(b.password) - this.getPasswordStrength(a.password);
          case 'date':
          default:
            return new Date(b.updatedAt || b.createdAt) - new Date(a.updatedAt || a.createdAt);
        }
      });

      return filtered;
    },
}

    // 监听器
    watch: {
      'currentPassword.url'(newVal) {
        this.updateFavicon();
      },
      'currentPassword.website'(newVal) {
        this.autoFillWebsiteInfo();
      },
    },

    methods: {
      // 初始化空密码对象
      getEmptyPassword() {
        return {
          website: '',
          url: '',
          username: '',
          password: '',
          category: '',
          notes: '',
          tags: [],
          favorite: false,
          reminder: false,
          reminderDays: 90,
          twoFactor: '',
          createdAt: new Date(),
          updatedAt: null,
          favicon: '',
        };
      },

      // 密码强度计算
      getPasswordStrength(password) {
        if (!password) return 0;

        let strength = 0;

        if (password.length >= 8) strength++;
        if (password.length >= 12) strength++;
        if (/[a-z]/.test(password)) strength++;
        if (/[A-Z]/.test(password)) strength++;
        if (/[0-9]/.test(password)) strength++;
        if (/[^A-Za-z0-9]/.test(password)) strength++;

        return Math.min(4, strength);
      },

      getStrengthText(password) {
        const strength = this.getPasswordStrength(password);
        const texts = ['', '很弱', '较弱', '中等', '强密码'];
        return texts[strength] || '很弱';
      },

      getStrengthClass(password) {
        const strength = this.getPasswordStrength(password);
        const classes = ['', 'very-weak', 'weak', 'medium', 'strong'];
        return classes[strength] || 'very-weak';
      },

      getStrengthLevel(password) {
        const strength = this.getPasswordStrength(password);
        if (strength >= 3) return 'strong';
        if (strength === 2) return 'medium';
        return 'weak';
      },

      getStrengthDotClass(password, index) {
        const strength = this.getPasswordStrength(password);
        if (index <= strength) {
          if (strength >= 3) return 'strong';
          if (strength === 2) return 'medium';
          return 'weak';
        }
        return '';
      },

      getPasswordStrengthBarClass(index) {
        const strength = this.getPasswordStrength(this.currentPassword.password);
        if (index <= strength) {
          if (strength >= 3) return 'strong';
          if (strength === 2) return 'medium';
          return 'weak';
        }
        return '';
      },

      // 分类相关
      getCategoryIcon(category) {
        const icons = {
          social: 'el-icon-chat-line-round',
          email: 'el-icon-message',
          work: 'el-icon-suitcase',
          shopping: 'el-icon-shopping-cart-full',
          bank: 'el-icon-coin',
          entertainment: 'el-icon-video-play',
          other: 'el-icon-more',
        };
        return icons[category] || 'el-icon-more';
      },

      getCategoryClass(category) {
        return `category-${category}`;
      },

      getCategoryName(category) {
        const names = {
          social: '社交',
          email: '邮箱',
          work: '工作',
          shopping: '购物',
          bank: '银行',
          entertainment: '娱乐',
          other: '其他',
        };
        return names[category] || '其他';
      },

      getCategoryTagType(category) {
        const types = {
          social: 'primary',
          email: 'success',
          work: 'warning',
          shopping: 'danger',
          bank: 'info',
          entertainment: '',
          other: '',
        };
        return types[category] || '';
      },

      // 日期格式化
      formatDate(date) {
        if (!date) return '';
        const d = new Date(date);
        const now = new Date();
        const diff = now.getTime() - d.getTime();
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));

        if (days === 0) return '今天';
        if (days === 1) return '昨天';
        if (days < 7) return `${days}天前`;
        if (days < 30) return `${Math.floor(days / 7)}周前`;
        if (days < 365) return `${Math.floor(days / 30)}月前`;

        return d.getFullYear() + '/' + (d.getMonth() + 1) + '/' + d.getDate();
      },

      formatDateTime(date) {
        if (!date) return '';
        const d = new Date(date);
        return (
          d.getFullYear() +
          '-' +
          String(d.getMonth() + 1).padStart(2, '0') +
          '-' +
          String(d.getDate()).padStart(2, '0') +
          ' ' +
          String(d.getHours()).padStart(2, '0') +
          ':' +
          String(d.getMinutes()).padStart(2, '0')
        );
      },

      // 复制到剪贴板
      async copyToClipboard(text, type = '内容') {
        try {
          await navigator.clipboard.writeText(text);
          this.$message.success(`${type}已复制到剪贴板`);
        } catch (err) {
          // 兼容旧浏览器
          const textArea = document.createElement('textarea');
          textArea.value = text;
          document.body.appendChild(textArea);
          textArea.focus();
          textArea.select();
          try {
            document.execCommand('copy');
            this.$message.success(`${type}已复制到剪贴板`);
          } catch (err) {
            this.$message.error('复制失败');
          }
          document.body.removeChild(textArea);
        }
      },

      // 打开网站
      openWebsite(url) {
        if (!url.startsWith('http://') && !url.startsWith('https://')) {
          url = 'https://' + url;
        }
        window.open(url, '_blank');
      },

      // 搜索处理
      handleSearch() {
        // 实时搜索，这里可以添加防抖逻辑
      },

      // 高级搜索
      advancedSearch() {
        this.$message.info('高级搜索功能开发中...');
      },

      // 选择控制
      handleSelection() {
        this.allSelected = this.selectedPasswords.length === this.passwords.length;
      },

      handleTableSelection(selection) {
        this.selectedPasswords = selection.map((item) => item.id);
      },

      selectAll() {
        if (this.allSelected) {
          this.selectedPasswords = [];
        } else {
          this.selectedPasswords = this.passwords.map((p) => p.id);
        }
        this.allSelected = !this.allSelected;
      },

      deleteSelected() {
        this.$confirm('确定要删除选中的密码吗？', '批量删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          this.passwords = this.passwords.filter((p) => !this.selectedPasswords.includes(p.id));
          this.selectedPasswords = [];
          this.allSelected = false;
          this.$message.success('删除成功');
        });
      },

      // 表格行样式
      tableRowClassName({ row, rowIndex }) {
        if (this.selectedPasswords.includes(row.id)) {
          return 'selected-row';
        }
        return '';
      },

      // 密码操作
      showAddDialog() {
        this.currentPassword = this.getEmptyPassword();
        this.isEdit = false;
        this.dialogVisible = true;
        this.activeTab = 'basic';
        this.showPassword = false;
        this.generatePassword(); // 预生成一个密码
      },

      editPassword(password) {
        this.currentPassword = JSON.parse(JSON.stringify(password));
        this.isEdit = true;
        this.dialogVisible = true;
        this.activeTab = 'basic';
        this.showPassword = false;
      },

      editFromDetail() {
        this.editPassword(this.selectedPasswordDetail);
        this.detailDialogVisible = false;
      },

      deletePassword(password) {
        this.$confirm(`确定要删除 "${password.website}" 的密码吗？`, '删除确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }).then(() => {
          const index = this.passwords.findIndex((p) => p.id === password.id);
          if (index > -1) {
            this.passwords.splice(index, 1);
            this.$message.success('删除成功');
          }
        });
      },

      savePassword() {
        this.$refs.passwordForm.validate((valid) => {
          if (valid) {
            this.saving = true;

            // 模拟保存过程
            setTimeout(() => {
              if (this.isEdit) {
                const index = this.passwords.findIndex((p) => p.id === this.currentPassword.id);
                if (index > -1) {
                  this.currentPassword.updatedAt = new Date();
                  this.passwords.splice(index, 1, JSON.parse(JSON.stringify(this.currentPassword)));
                }
                this.$message.success('密码更新成功');
              } else {
                this.currentPassword.id = Date.now();
                this.currentPassword.createdAt = new Date();
                this.passwords.unshift(JSON.parse(JSON.stringify(this.currentPassword)));
                this.$message.success('密码添加成功');
              }

              this.saving = false;
              this.dialogVisible = false;
            }, 1000);
          }
        });
      },

      savePasswordAsDraft() {
        this.$message.info('草稿保存功能开发中...');
      },

      // 密码详情
      viewPasswordDetail(password) {
        this.selectedPasswordDetail = password;
        this.showDetailPassword = false;
        this.detailDialogVisible = true;
      },

      // 收藏切换
      toggleFavorite(password) {
        password.favorite = !password.favorite;
        this.$message.success(password.favorite ? '已添加到收藏' : '已取消收藏');
      },

      // 项目菜单操作
      handleItemAction(command) {
        switch (command.action) {
          case 'edit':
            this.editPassword(command.item);
            break;
          case 'duplicate':
            this.duplicatePassword(command.item);
            break;
          case 'share':
            this.sharePassword(command.item);
            break;
          case 'delete':
            this.deletePassword(command.item);
            break;
        }
      },

      duplicatePassword(password) {
        const duplicate = JSON.parse(JSON.stringify(password));
        duplicate.id = Date.now();
        duplicate.website = password.website + ' (副本)';
        duplicate.createdAt = new Date();
        duplicate.updatedAt = null;
        this.passwords.unshift(duplicate);
        this.$message.success('密码已复制');
      },

      sharePassword(password) {
        this.$message.info('分享功能开发中...');
      },

      // 密码生成
      generatePassword() {
        let charset = '';
        if (this.includeUppercase) charset += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        if (this.includeLowercase) charset += 'abcdefghijklmnopqrstuvwxyz';
        if (this.includeNumbers) charset += '0123456789';
        if (this.includeSymbols) charset += '!@#$%^&*()_+-=[]{}|;:,.<>?';

        if (this.excludeSimilar) {
          charset = charset.replace(/[0Ol1I]/g, '');
        }

        if (charset.length === 0) {
          this.generatedPassword = '';
          return;
        }

        let password = '';
        for (let i = 0; i < this.passwordLength; i++) {
          password += charset.charAt(Math.floor(Math.random() * charset.length));
        }

        this.generatedPassword = password;
      },

      generateRandomPassword() {
        this.generatorDialogVisible = true;
        this.generatePassword();
      },

      generateStrongPassword() {
        this.passwordLength = 16;
        this.includeUppercase = true;
        this.includeLowercase = true;
        this.includeNumbers = true;
        this.includeSymbols = true;
        this.excludeSimilar = true;
        this.generatePassword();
        this.currentPassword.password = this.generatedPassword;
      },

      useGeneratedPassword() {
        this.currentPassword.password = this.generatedPassword;
        this.generatorDialogVisible = false;
        this.updatePasswordStrength();
      },

      updatePasswordStrength() {
        // 触发密码强度更新
        this.$forceUpdate();
      },

      // 图标处理
      handleImageError(event) {
        event.target.style.display = 'none';
      },

      autoFillWebsiteInfo() {
        if (!this.currentPassword.url && this.currentPassword.website) {
          this.currentPassword.url = `https://${this.currentPassword.website.toLowerCase().replace(/\s+/g, '')}.com`;
          this.updateFavicon();
        }
      },

      updateFavicon() {
        if (this.currentPassword.url) {
          try {
            const url = new URL(this.currentPassword.url);
            this.currentPassword.favicon = `${url.protocol}//${url.hostname}/favicon.ico`;
          } catch (e) {
            this.currentPassword.favicon = '';
          }
        }
      },

      // 标签管理
      removeTag(tag) {
        const index = this.currentPassword.tags.indexOf(tag);
        if (index > -1) {
          this.currentPassword.tags.splice(index, 1);
        }
      },

      showInput() {
        this.inputVisible = true;
        this.$nextTick(() => {
          this.$refs.saveTagInput.$refs.input.focus();
        });
      },

      handleInputConfirm() {
        const inputValue = this.inputValue;
        if (inputValue && !this.currentPassword.tags.includes(inputValue)) {
          this.currentPassword.tags.push(inputValue);
        }
        this.inputVisible = false;
        this.inputValue = '';
      },

      // 导入导出
      showImportDialog() {
        this.importDialogVisible = true;
      },

      handleFileChange(file) {
        this.importFile = file.raw;
      },

      importData() {
        if (!this.importFile) {
          this.$message.warning('请选择要导入的文件');
          return;
        }

        this.importing = true;

        const reader = new FileReader();
        reader.onload = (e) => {
          try {
            let data;
            if (this.importFile.name.endsWith('.json')) {
              data = JSON.parse(e.target.result);
            } else if (this.importFile.name.endsWith('.csv')) {
              data = this.parseCSV(e.target.result);
            } else {
              throw new Error('不支持的文件格式');
            }

            this.processImportData(data);
          } catch (error) {
            this.$message.error('文件解析失败: ' + error.message);
          } finally {
            this.importing = false;
          }
        };

        reader.readAsText(this.importFile);
      },

      parseCSV(csvText) {
        // 简单的CSV解析，实际项目中建议使用专门的CSV解析库
        const lines = csvText.split('\n');
        const headers = lines[0].split(',');
        const data = [];

        for (let i = 1; i < lines.length; i++) {
          if (lines[i].trim()) {
            const values = lines[i].split(',');
            const obj = {};
            headers.forEach((header, index) => {
              obj[header.trim()] = values[index] ? values[index].trim() : '';
            });
            data.push(obj);
          }
        }

        return data;
      },

      processImportData(data) {
        let imported = 0;
        let skipped = 0;

        data.forEach((item) => {
          if (this.importOptions.skipDuplicate) {
            const exists = this.passwords.some(
              (p) => p.website === item.website && p.username === item.username
            );
            if (exists) {
              skipped++;
              return;
            }
          }

          const password = {
            id: Date.now() + Math.random(),
            website: item.website || '',
            url: item.url || '',
            username: item.username || '',
            password: item.password || '',
            category: item.category || 'other',
            notes: item.notes || '',
            tags: item.tags ? item.tags.split(',') : [],
            favorite: false,
            reminder: false,
            reminderDays: 90,
            twoFactor: '',
            createdAt: new Date(),
            updatedAt: null,
            favicon: '',
          };

          this.passwords.unshift(password);
          imported++;
        });

        this.$message.success(`导入完成：${imported} 条记录已导入，${skipped} 条记录已跳过`);
        this.importDialogVisible = false;
      },

      exportData() {
        const data = this.passwords.map((p) => ({
          website: p.website,
          url: p.url,
          username: p.username,
          password: p.password,
          category: p.category,
          notes: p.notes,
          tags: p.tags.join(','),
          createdAt: p.createdAt,
          updatedAt: p.updatedAt,
        }));

        const jsonStr = JSON.stringify(data, null, 2);
        const blob = new Blob([jsonStr], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `passwords_backup_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);

        this.$message.success('数据导出成功');
      },

      // 安全检测
      runSecurityCheck() {
        this.securityIssues = [];

        // 检测弱密码
        const weakPasswords = this.passwords.filter(
          (p) => this.getPasswordStrength(p.password) <= 1
        );
        if (weakPasswords.length > 0) {
          this.securityIssues.push({
            type: 'weak',
            title: '弱密码',
            description: '这些密码容易被破解，建议使用更强的密码',
            severity: 'high',
            count: weakPasswords.length,
            passwords: weakPasswords,
          });
        }

        // 检测重复密码
        const duplicateGroups = this.getDuplicatePasswords();
        if (duplicateGroups.length > 0) {
          this.securityIssues.push({
            type: 'duplicate',
            title: '重复密码',
            description: '多个账户使用相同密码，建议为每个账户设置唯一密码',
            severity: 'medium',
            count: duplicateGroups.reduce((sum, group) => sum + group.length, 0),
            passwords: duplicateGroups.flat(),
          });
        }

        // 检测旧密码
        const oldPasswords = this.passwords.filter((p) => {
          const days = (new Date() - new Date(p.updatedAt || p.createdAt)) / (1000 * 60 * 60 * 24);
          return days > 90;
        });
        if (oldPasswords.length > 0) {
          this.securityIssues.push({
            type: 'old',
            title: '密码过旧',
            description: '这些密码已经很久没有更新，建议定期更换密码',
            severity: 'medium',
            count: oldPasswords.length,
            passwords: oldPasswords,
          });
        }

        this.securityReportVisible = true;
      },

      getDuplicatePasswords() {
        const passwordGroups = {};

        this.passwords.forEach((p) => {
          if (!passwordGroups[p.password]) {
            passwordGroups[p.password] = [];
          }
          passwordGroups[p.password].push(p);
        });

        return Object.values(passwordGroups).filter((group) => group.length > 1);
      },

      getDuplicatePasswordCount() {
        const duplicateGroups = this.getDuplicatePasswords();
        return duplicateGroups.reduce((sum, group) => sum + group.length, 0);
      },

      getSecurityColor() {
        if (this.securityScore >= 80) return '#67c23a';
        if (this.securityScore >= 60) return '#e6a23c';
        return '#f56c6c';
      },

      getSecurityLevel() {
        if (this.securityScore >= 80) return '安全状况良好';
        if (this.securityScore >= 60) return '需要改进';
        return '存在安全风险';
      },

      showAffectedPasswords(issue) {
        this.$message.info(`显示受影响的密码列表功能开发中...`);
      },

      // UI控制
      switchView(mode) {
        this.viewMode = mode;
      },

      showSettings() {
        this.$message.info('设置页面开发中...');
      },

      showProfile() {
        this.$message.info('个人资料页面开发中...');
      },

      // 生命周期
      mounted() {
        this.initParticleEffect();
        this.generatePassword();
      },

      // 粒子效果
      initParticleEffect() {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = window.innerWidth;
        canvas.height = window.innerHeight;
        canvas.style.position = 'fixed';
        canvas.style.top = '0';
        canvas.style.left = '0';
        canvas.style.pointerEvents = 'none';
        canvas.style.zIndex = '1';
        this.$refs.particleContainer.appendChild(canvas);

        const particles = [];

        for (let i = 0; i < 50; i++) {
          particles.push({
            x: Math.random() * canvas.width,
            y: Math.random() * canvas.height,
            vx: (Math.random() - 0.5) * 0.5,
            vy: (Math.random() - 0.5) * 0.5,
            radius: Math.random() * 2 + 1,
            opacity: Math.random() * 0.5 + 0.1,
          });
        }

        const animate = () => {
          ctx.clearRect(0, 0, canvas.width, canvas.height);

          particles.forEach((particle) => {
            particle.x += particle.vx;
            particle.y += particle.vy;

            if (particle.x < 0 || particle.x > canvas.width) particle.vx *= -1;
            if (particle.y < 0 || particle.y > canvas.height) particle.vy *= -1;

            ctx.beginPath();
            ctx.arc(particle.x, particle.y, particle.radius, 0, Math.PI * 2);
            ctx.fillStyle = `rgba(255, 255, 255, ${particle.opacity})`;
            ctx.fill();
          });

          requestAnimationFrame(animate);
        };

        animate();
      },
    },
};
</script>

<style lang="scss" scoped>
/* 主样式 */
.password-manager {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow-x: hidden;
}

/* 粒子容器 */
.particle-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

/* 背景装饰 */
.background-decoration {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 2;
  pointer-events: none;

  .shape {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;

    &.shape1 {
      width: 300px;
      height: 300px;
      top: 10%;
      left: -10%;
      animation-delay: 0s;
    }

    &.shape2 {
      width: 200px;
      height: 200px;
      top: 60%;
      right: -5%;
      animation-delay: 2s;
    }

    &.shape3 {
      width: 150px;
      height: 150px;
      bottom: 20%;
      left: 10%;
      animation-delay: 4s;
    }

    &.shape4 {
      width: 100px;
      height: 100px;
      top: 30%;
      right: 20%;
      animation-delay: 1s;
    }

    &.shape5 {
      width: 250px;
      height: 250px;
      bottom: -10%;
      right: 30%;
      animation-delay: 3s;
    }

    &.shape6 {
      width: 180px;
      height: 180px;
      top: 5%;
      left: 70%;
      animation-delay: 5s;
    }
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* 玻璃卡片效果 */
.glass-card {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.25);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 40px 0 rgba(31, 38, 135, 0.35);
  }
}

/* 顶部导航 */
.top-nav {
  position: sticky;
  top: 20px;
  z-index: 100;
  padding: 20px 20px 0;

  .nav-card {
    padding: 15px 25px;
  }

  .nav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .logo-section {
    display: flex;
    align-items: center;
    gap: 12px;

    .logo-icon {
      width: 40px;
      height: 40px;
      background: linear-gradient(45deg, #667eea, #764ba2);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 18px;
    }

    .logo-text {
      font-size: 24px;
      font-weight: 700;
      color: white;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }

  .nav-actions {
    display: flex;
    gap: 5px;

    .nav-btn {
      color: white !important;
      font-size: 18px;
      padding: 10px;
      border-radius: 10px;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: scale(1.05);
      }
    }
  }
}

/* 主容器 */
.main-container {
  padding: 20px;
  position: relative;
  z-index: 10;
}

/* 统计仪表板 */
.dashboard-section {
  margin-bottom: 30px;

  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
  }

  .stat-card {
    padding: 25px;
    position: relative;
    overflow: hidden;

    &.total-passwords {
      background: linear-gradient(
        135deg,
        rgba(102, 126, 234, 0.3) 0%,
        rgba(118, 75, 162, 0.3) 100%
      );
    }

    &.strong-passwords {
      background: linear-gradient(135deg, rgba(103, 194, 58, 0.3) 0%, rgba(76, 175, 80, 0.3) 100%);
    }

    &.weak-passwords {
      background: linear-gradient(135deg, rgba(245, 108, 108, 0.3) 0%, rgba(229, 57, 53, 0.3) 100%);
    }

    &.recent-added {
      background: linear-gradient(135deg, rgba(255, 152, 0, 0.3) 0%, rgba(251, 140, 0, 0.3) 100%);
    }

    .stat-content {
      display: flex;
      align-items: center;
      gap: 20px;
      z-index: 2;
      position: relative;
    }

    .stat-icon {
      width: 60px;
      height: 60px;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 15px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28px;
      color: white;
    }

    .stat-info {
      flex: 1;

      h3 {
        font-size: 36px;
        font-weight: 700;
        color: white;
        margin: 0 0 5px 0;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
      }

      p {
        font-size: 16px;
        color: rgba(255, 255, 255, 0.9);
        margin: 0;
      }
    }

    .stat-chart {
      position: absolute;
      right: 20px;
      top: 50%;
      transform: translateY(-50%);

      .chart-ring {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: conic-gradient(
          rgba(255, 255, 255, 0.8) 0deg,
          rgba(255, 255, 255, 0.8) calc(var(--progress) * 3.6deg),
          rgba(255, 255, 255, 0.2) calc(var(--progress) * 3.6deg),
          rgba(255, 255, 255, 0.2) 360deg
        );
        position: relative;

        &::after {
          content: '';
          position: absolute;
          top: 10px;
          left: 10px;
          width: 60px;
          height: 60px;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 50%;
          backdrop-filter: blur(5px);
        }
      }
    }

    .strength-indicator {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-top: 10px;

      .strength-bar {
        height: 6px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 3px;
        flex: 1;
        position: relative;
        overflow: hidden;

        &.strong::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          height: 100%;
          width: var(--width, 100%);
          background: linear-gradient(90deg, #67c23a, #85ce61);
          border-radius: 3px;
        }
      }
    }
  }
}

/* 快速操作 */
.quick-actions {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;

  .action-btn {
    padding: 12px 24px;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s ease;

    &.primary-action {
      background: linear-gradient(45deg, #667eea, #764ba2);
      border: none;
      color: white;

      &:hover {
        background: linear-gradient(45deg, #5a6fd8, #6a4190);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
      }
    }

    &.secondary-action {
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;

      &:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-1px);
      }
    }
  }
}

/* 工具栏 */
.toolbar-section {
  margin-bottom: 25px;

  .toolbar-card {
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
  }

  .toolbar-left {
    display: flex;
    gap: 15px;
    align-items: center;
    flex: 1;
  }

  .search-input {
    min-width: 300px;

    ::v-deep .el-input__inner {
      background: rgba(255, 255, 255, 0.9);
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 12px;
      padding-left: 40px;
    }
  }

  .category-select,
  .strength-select,
  .sort-select {
    min-width: 150px;

    ::v-deep .el-input__inner {
      background: rgba(255, 255, 255, 0.9);
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 12px;
    }
  }

  .view-controls {
    display: flex;
    gap: 10px;

    .view-btn {
      width: 40px;
      height: 40px;
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;
      cursor: pointer;
      transition: all 0.3s ease;

      &.active {
        background: rgba(255, 255, 255, 0.9);
        color: #667eea;
      }

      &:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.05);
      }
    }
  }

  .bulk-actions {
    display: flex;
    gap: 10px;
    align-items: center;

    .select-info {
      color: white;
      font-size: 14px;
      margin-right: 10px;
    }
  }
}

/* 密码列表 */
.password-content {
  .password-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
  }

  .password-card {
    padding: 25px;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 15px 45px 0 rgba(31, 38, 135, 0.4);
    }

    &.selected {
      border: 2px solid rgba(255, 255, 255, 0.8);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 15px;
    }

    .website-info {
      display: flex;
      align-items: center;
      gap: 15px;
      flex: 1;

      .website-icon {
        width: 45px;
        height: 45px;
        border-radius: 12px;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        color: white;
        overflow: hidden;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 12px;
        }
      }

      .website-details {
        h3 {
          font-size: 18px;
          font-weight: 700;
          color: white;
          margin: 0 0 5px 0;
          cursor: pointer;

          &:hover {
            text-decoration: underline;
          }
        }

        p {
          font-size: 14px;
          color: rgba(255, 255, 255, 0.8);
          margin: 0;
        }
      }
    }

    .card-actions {
      display: flex;
      gap: 5px;

      .action-btn {
        width: 35px;
        height: 35px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255, 255, 255, 0.2);
        border: none;
        color: white;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 16px;

        &:hover {
          background: rgba(255, 255, 255, 0.3);
          transform: scale(1.1);
        }

        &.favorite.active {
          color: #ffd700;
        }
      }
    }

    .card-content {
      .user-info {
        margin-bottom: 15px;

        .username {
          font-size: 16px;
          color: white;
          font-weight: 500;
          word-break: break-word;
        }
      }

      .password-info {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 15px;

        .password-field {
          flex: 1;
          background: rgba(255, 255, 255, 0.2);
          border: 1px solid rgba(255, 255, 255, 0.3);
          border-radius: 8px;
          padding: 8px 12px;
          color: white;
          font-family: 'Courier New', monospace;
          font-size: 14px;
          min-height: 20px;
        }

        .copy-btn {
          padding: 8px;
          background: rgba(255, 255, 255, 0.2);
          border: 1px solid rgba(255, 255, 255, 0.3);
          border-radius: 8px;
          color: white;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            background: rgba(255, 255, 255, 0.3);
          }
        }
      }

      .strength-display {
        display: flex;
        align-items: center;
        gap: 10px;
        margin-bottom: 15px;

        .strength-dots {
          display: flex;
          gap: 4px;

          .dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;

            &.strong {
              background: #67c23a;
            }

            &.medium {
              background: #e6a23c;
            }

            &.weak {
              background: #f56c6c;
            }
          }
        }

        .strength-text {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.8);
          font-weight: 500;
        }
      }

      .card-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .category-tag {
          padding: 4px 12px;
          border-radius: 15px;
          font-size: 12px;
          font-weight: 500;
          background: rgba(255, 255, 255, 0.2);
          color: white;
          border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .last-updated {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.7);
        }
      }

      .tags-list {
        display: flex;
        flex-wrap: wrap;
        gap: 5px;
        margin-top: 10px;

        .tag {
          padding: 2px 8px;
          background: rgba(255, 255, 255, 0.2);
          border: 1px solid rgba(255, 255, 255, 0.3);
          border-radius: 10px;
          font-size: 11px;
          color: rgba(255, 255, 255, 0.9);
        }
      }
    }

    .selection-checkbox {
      position: absolute;
      top: 15px;
      left: 15px;
    }
  }
}

/* 表格视图 */
.password-table {
  .glass-card {
    padding: 0;
    overflow: hidden;
  }

  ::v-deep .el-table {
    background: transparent;

    .el-table__header-wrapper {
      background: rgba(255, 255, 255, 0.1);

      th {
        background: transparent;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        font-weight: 600;
      }
    }

    .el-table__body-wrapper {
      tr {
        background: transparent;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);

        &.selected-row {
          background: rgba(255, 255, 255, 0.1);
        }

        &:hover {
          background: rgba(255, 255, 255, 0.05);
        }

        td {
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
          color: white;
        }
      }
    }
  }

  .table-website {
    display: flex;
    align-items: center;
    gap: 10px;

    .website-favicon {
      width: 24px;
      height: 24px;
      border-radius: 4px;
      background: rgba(255, 255, 255, 0.2);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;

      img {
        width: 100%;
        height: 100%;
        border-radius: 4px;
      }
    }
  }

  .table-password {
    font-family: 'Courier New', monospace;
    background: rgba(255, 255, 255, 0.1);
    padding: 4px 8px;
    border-radius: 4px;
    display: inline-block;
  }

  .table-actions {
    display: flex;
    gap: 5px;

    .el-button {
      padding: 5px;
      min-width: 30px;
    }
  }
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: white;

  .empty-icon {
    font-size: 80px;
    color: rgba(255, 255, 255, 0.5);
    margin-bottom: 20px;
  }

  h3 {
    font-size: 24px;
    margin-bottom: 10px;
    color: white;
  }

  p {
    font-size: 16px;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 30px;
  }
}

/* 对话框样式 */
::v-deep .el-dialog {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 20px 60px 0 rgba(31, 38, 135, 0.3);

  .el-dialog__header {
    background: linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border-radius: 20px 20px 0 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);

    .el-dialog__title {
      font-size: 20px;
      font-weight: 700;
      color: #333;
    }
  }

  .el-dialog__body {
    padding: 30px;
  }

  .el-dialog__footer {
    border-top: 1px solid rgba(255, 255, 255, 0.2);
    background: rgba(255, 255, 255, 0.5);
    border-radius: 0 0 20px 20px;
  }
}

/* 表单样式 */
.dialog-form {
  .form-tabs {
    margin-bottom: 25px;

    ::v-deep .el-tabs__header {
      margin-bottom: 20px;

      .el-tabs__nav-wrap::after {
        background: rgba(255, 255, 255, 0.2);
      }

      .el-tabs__active-bar {
        background: linear-gradient(45deg, #667eea, #764ba2);
      }

      .el-tabs__item {
        color: #666;
        font-weight: 500;

        &.is-active {
          color: #667eea;
          font-weight: 600;
        }
      }
    }
  }

  .el-form-item {
    margin-bottom: 25px;

    .el-form-item__label {
      font-weight: 600;
      color: #333;
      font-size: 14px;
    }

    .el-input,
    .el-select,
    .el-textarea {
      .el-input__inner,
      .el-textarea__inner {
        border: 2px solid rgba(102, 126, 234, 0.2);
        border-radius: 12px;
        padding: 12px 15px;
        font-size: 14px;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.9);

        &:focus {
          border-color: #667eea;
          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
      }
    }
  }

  .form-row {
    display: flex;
    gap: 20px;

    .el-form-item {
      flex: 1;
    }
  }

  .password-input-group {
    position: relative;

    .password-actions {
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
      display: flex;
      gap: 5px;
      z-index: 10;

      .el-button {
        padding: 5px;
        min-width: 30px;
        height: 30px;
        border-radius: 6px;
        border: 1px solid rgba(102, 126, 234, 0.3);
        background: rgba(255, 255, 255, 0.9);
        color: #667eea;

        &:hover {
          background: rgba(102, 126, 234, 0.1);
        }
      }
    }

    .el-input__inner {
      padding-right: 100px;
    }
  }

  .strength-meter {
    margin-top: 10px;

    .strength-bar {
      height: 6px;
      background: #f0f0f0;
      border-radius: 3px;
      overflow: hidden;
      margin-bottom: 5px;

      .strength-fill {
        height: 100%;
        border-radius: 3px;
        transition: all 0.3s ease;

        &.weak {
          background: linear-gradient(45deg, #f56c6c, #ff8a80);
          width: 25%;
        }

        &.medium {
          background: linear-gradient(45deg, #e6a23c, #ffb74d);
          width: 60%;
        }

        &.strong {
          background: linear-gradient(45deg, #67c23a, #81c784);
          width: 100%;
        }
      }
    }

    .strength-text {
      font-size: 12px;
      color: #666;
      font-weight: 500;
    }
  }

  .form-tags {
    .el-tag {
      margin: 0 5px 5px 0;
      background: rgba(102, 126, 234, 0.1);
      border-color: rgba(102, 126, 234, 0.3);
      color: #667eea;
      border-radius: 15px;
    }

    .el-input {
      width: 100px;
    }
  }

  .form-section {
    margin-bottom: 30px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 15px;
      display: flex;
      align-items: center;
      gap: 10px;

      .section-icon {
        color: #667eea;
      }
    }

    .section-content {
      padding: 20px;
      background: rgba(102, 126, 234, 0.05);
      border-radius: 12px;
      border: 1px solid rgba(102, 126, 234, 0.1);
    }
  }
}

/* 密码生成器样式 */
.generator-content {
  .generated-password {
    margin-bottom: 25px;

    .password-output {
      .el-input__inner {
        font-family: 'Courier New', monospace;
        font-size: 16px;
        font-weight: bold;
        text-align: center;
        background: linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
        border: 2px solid rgba(102, 126, 234, 0.3);
      }

      .el-input-group__append {
        background: linear-gradient(45deg, #667eea, #764ba2);
        border: none;

        .el-button {
          background: transparent;
          border: none;
          color: white;
          font-size: 16px;
        }
      }
    }
  }

  .generator-options {
    .option-group {
      margin-bottom: 20px;

      label {
        color: #333;
        font-weight: 500;
        font-size: 14px;
        display: block;
        margin-bottom: 10px;
      }

      .el-slider {
        margin: 15px 0;

        ::v-deep .el-slider__bar {
          background: linear-gradient(45deg, #667eea, #764ba2);
        }

        ::v-deep .el-slider__button {
          border: 2px solid #667eea;
          background: white;
          width: 20px;
          height: 20px;
        }
      }

      .el-checkbox {
        ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
          background: #667eea;
          border-color: #667eea;
        }

        ::v-deep .el-checkbox__label {
          color: #333;
          font-weight: 500;
        }
      }
    }
  }

  .generator-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
  }
}

/* 密码详情 */
.password-detail {
  .detail-header {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(45deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    border-radius: 12px;
    border: 1px solid rgba(102, 126, 234, 0.2);

    .detail-icon {
      width: 60px;
      height: 60px;
      border-radius: 15px;
      background: rgba(102, 126, 234, 0.2);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
      color: #667eea;

      img {
        width: 100%;
        height: 100%;
        border-radius: 15px;
      }
    }

    .detail-title {
      h2 {
        font-size: 24px;
        color: #333;
        margin: 0 0 5px 0;
      }

      p {
        color: #666;
        margin: 0;
      }
    }
  }

  .detail-content {
    .detail-item {
      margin-bottom: 20px;
      padding: 15px;
      background: rgba(255, 255, 255, 0.5);
      border-radius: 10px;
      border: 1px solid rgba(102, 126, 234, 0.1);

      label {
        display: block;
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
        font-size: 14px;
      }

      .detail-value {
        display: flex;
        align-items: center;
        gap: 10px;

        span {
          flex: 1;
          font-size: 14px;
          color: #666;
          word-break: break-all;
        }

        .password-value {
          font-family: 'Courier New', monospace;
          background: rgba(102, 126, 234, 0.1);
          padding: 8px 12px;
          border-radius: 6px;
          border: 1px solid rgba(102, 126, 234, 0.2);
        }

        .el-button {
          padding: 5px 10px;
          font-size: 12px;
        }
      }
    }
  }
}

/* 安全报告 */
.security-report {
  .report-header {
    text-align: center;
    margin-bottom: 30px;

    .security-score {
      display: inline-block;
      width: 120px;
      height: 120px;
      border-radius: 50%;
      background: conic-gradient(
        from 0deg,
        #f56c6c 0deg 60deg,
        #e6a23c 60deg 180deg,
        #67c23a 180deg 360deg
      );
      position: relative;
      margin-bottom: 20px;

      .score-inner {
        position: absolute;
        top: 10px;
        left: 10px;
        width: 100px;
        height: 100px;
        background: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;

        .score-number {
          font-size: 28px;
          font-weight: bold;
          color: #333;
        }

        .score-text {
          font-size: 12px;
          color: #666;
          font-weight: 500;
        }
      }
    }

    .report-summary {
      h3 {
        color: #333;
        margin-bottom: 10px;
      }

      p {
        color: #666;
        margin: 0;
      }
    }
  }

  .issues-list {
    .issue-item {
      display: flex;
      align-items: center;
      gap: 15px;
      padding: 20px;
      background: rgba(255, 255, 255, 0.7);
      border-radius: 12px;
      border: 1px solid rgba(102, 126, 234, 0.1);
      margin-bottom: 15px;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.9);
        transform: translateX(5px);
      }

      .issue-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 20px;

        &.high {
          background: rgba(245, 108, 108, 0.2);
          color: #f56c6c;
        }

        &.medium {
          background: rgba(230, 162, 60, 0.2);
          color: #e6a23c;
        }

        &.low {
          background: rgba(103, 194, 58, 0.2);
          color: #67c23a;
        }
      }

      .issue-content {
        flex: 1;

        .issue-title {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          margin-bottom: 5px;
        }

        .issue-description {
          font-size: 14px;
          color: #666;
          margin-bottom: 5px;
        }

        .issue-count {
          font-size: 12px;
          color: #999;
        }
      }

      .issue-actions {
        .el-button {
          padding: 8px 16px;
          border-radius: 8px;
        }
      }
    }
  }

  .no-issues {
    text-align: center;
    padding: 40px;
    color: #67c23a;

    .success-icon {
      font-size: 60px;
      margin-bottom: 20px;
    }

    h3 {
      color: #67c23a;
      margin-bottom: 10px;
    }

    p {
      color: #666;
      margin: 0;
    }
  }
}

/* 导入界面 */
.import-content {
  .upload-demo {
    margin-bottom: 30px;

    ::v-deep .el-upload-dragger {
      background: rgba(102, 126, 234, 0.05);
      border: 2px dashed rgba(102, 126, 234, 0.3);
      border-radius: 12px;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(102, 126, 234, 0.1);
        border-color: #667eea;
      }

      .el-icon-upload {
        font-size: 48px;
        color: #667eea;
        margin-bottom: 20px;
      }

      .el-upload__text {
        color: #333;
        font-size: 16px;
        font-weight: 500;
      }
    }

    .el-upload__tip {
      color: #666;
      font-size: 14px;
    }
  }

  .import-options {
    h4 {
      color: #333;
      margin-bottom: 15px;
      font-size: 16px;
      font-weight: 600;
    }

    .el-checkbox {
      display: block;
      margin-bottom: 10px;

      ::v-deep .el-checkbox__label {
        color: #333;
        font-weight: 500;
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .password-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }
}

@media (max-width: 768px) {
  .main-container {
    padding: 15px;
  }

  .top-nav {
    padding: 15px 15px 0;

    .nav-card {
      padding: 12px 20px;
    }

    .logo-text {
      font-size: 20px;
    }
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .password-grid {
    grid-template-columns: 1fr;
  }

  .toolbar-card {
    flex-direction: column;
    align-items: stretch;
  }

  .toolbar-left {
    flex-direction: column;
    gap: 10px;
  }

  .search-input {
    min-width: auto;
  }

  .quick-actions {
    justify-content: center;
  }

  .dialog-form {
    .form-row {
      flex-direction: column;
      gap: 0;
    }
  }

  ::v-deep .el-dialog {
    width: 95% !important;
    margin: 0 auto;
  }
}

@media (max-width: 480px) {
  .password-card {
    padding: 20px;
  }

  .website-info {
    gap: 10px;

    .website-icon {
      width: 35px;
      height: 35px;
      font-size: 16px;
    }

    .website-details h3 {
      font-size: 16px;
    }
  }

  .card-actions {
    flex-direction: column;
    gap: 8px;
  }

  .action-btn {
    width: 100% !important;
    height: 40px !important;
    justify-content: flex-start !important;
    padding: 10px !important;
    font-size: 14px !important;

    &::after {
      content: attr(data-tooltip);
      margin-left: 8px;
      font-size: 12px;
    }
  }
}

/* 动画效果 */
@keyframes slideInUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.slide-in-up {
  animation: slideInUp 0.4s ease-out;
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 工具提示 */
.tooltip {
  position: relative;

  &:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
    animation: fadeIn 0.3s ease;
  }
}

/* 加载状态 */
.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;

  .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 成功/错误提示样式 */
.success-message {
  color: #67c23a;
  background: rgba(103, 194, 58, 0.1);
  border: 1px solid rgba(103, 194, 58, 0.3);
  padding: 12px 16px;
  border-radius: 8px;
  margin: 10px 0;
}

.error-message {
  color: #f56c6c;
  background: rgba(245, 108, 108, 0.1);
  border: 1px solid rgba(245, 108, 108, 0.3);
  padding: 12px 16px;
  border-radius: 8px;
  margin: 10px 0;
}

/* 过渡效果 */
.list-enter-active,
.list-leave-active {
  transition: all 0.3s ease;
}

.list-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.list-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

.list-move {
  transition: transform 0.3s ease;
}
</style>
